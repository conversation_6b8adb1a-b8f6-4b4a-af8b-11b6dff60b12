import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    // navigationStyle: 'custom',
    navigationBarTitleText: 'unibest',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
    enablePullDownRefresh: true,
    pullToRefresh: {
      support: true,
      color: '#d1302e',
      style: 'circle',
    },
  },
  easycom: {
    autoscan: true,
    custom: {
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  tabBar: {
    color: '#999999',
    selectedColor: '#D1302E',
    backgroundColor: '#F8F8F8',
    borderStyle: 'black',
    height: '60px',
    fontSize: '10px',
    iconWidth: '24px',
    spacing: '3px',
    list: [
      {
        iconPath: 'static/tabbar/home.png',
        selectedIconPath: 'static/tabbar/homeHL.png',
        pagePath: 'pages/index/index',
        text: '首页',
      },
      {
        iconPath: 'static/tabbar/match.png',
        selectedIconPath: 'static/tabbar/matchHL.png',
        pagePath: 'pages/ai/index',
        text: 'AI',
      },
      {
        iconPath: 'static/tabbar/match.png',
        selectedIconPath: 'static/tabbar/matchHL.png',
        pagePath: 'pages/competition/index',
        text: '赛事',
      },
      {
        iconPath: 'static/tabbar/personal.png',
        selectedIconPath: 'static/tabbar/personalHL.png',
        pagePath: 'pages/myInfo/index',
        text: '我的',
      },
    ],
  },
})
