// 全局要用的类型放到这里

type IResData<T> = {
  code: number
  msg: string
  data: T
}

// uni.uploadFile文件上传参数
type IUniUploadFileOptions = {
  file?: File
  files?: UniApp.UploadFileOptionFiles[]
  filePath?: string
  name?: string
  formData?: any
}

type IUserInfo = {
  id?: number
  nickname?: string
  avatarUrl?: string
  /** 微信的 openid，非微信没有这个字段 */
  openid?: string
  token?: string
  parentAccessToken?: string
  refreshToken?: string
  gold?: number
  balance?: number
  author?: number
  fans?: number
  articleNum?: number
  attentionNum?: number
  authorAuditStatus?: number
  privilegeSetStatus?: number
  pushStatus?: number
  kfStatus?: number
  isAuthor?: boolean
  parentId?: number
  qqmail?: string
  qq?: string
  competitionIds: null | string
  role: 0 | 1 // 0 老用户首页 1 新用户首页
  partner: 0 | 1 // 0 不是合伙人 1 是合伙人
  showPartner: number // 是否显示合作伙伴注册弹窗 0：否 1：是
  isPublicUser: boolean // 是否是公开用户
  isAttention: number // 0 开  1.关
  consume: number //  0.全部  1.未消费  2.已消费
  publicUser: number // 是否公域
  accomplishment: number // 0.不显示  1.显示
  multipleWx: number // 0.未绑定服务号  1.已绑定服务号
  captivePushAccount: number // 绑定的服务号ID
}

type IScan = {
  scanAuthorId: number
}

enum TestEnum {
  A = 'a',
  B = 'b',
}
