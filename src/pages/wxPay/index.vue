<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '神鱼体育-支付成功',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="px-[60rpx] text-center overflow-hidden">
    <wd-overlay show="true">
      <view class="wrapper">
        <view class="content">
          <view
            class="text-area text-center my-[40rpx] leading-[50rpx] font-weight-bold font-size-[40rpx]"
          >
            <view>支付成功!</view>
          </view>
          <view class="text-center">
            <view class="text-center my-[50rpx] text-[#666] font-size-[30rpx]">
              返回后请刷新页面，
              <text v-if="ordeData?.type == 1">，或重新扫码查看文章。</text>
              <text class="text-[#00c250]">{{ getTips() }}</text>
            </view>
            <wd-button @click="toPage" type="success">返回</wd-button>
          </view>
        </view>
      </view>
    </wd-overlay>
  </view>
</template>
<script lang="ts" setup>
import { getWxPayDetail } from '@/service/userService'
const sub_mch_id = ref()
const out_trade_no = ref()
const check_code = ref()
const ordeData = ref()
const msg = ref()
const getData = async () => {
  msg.value = '正在获取订单信息...'
  const data = await getWxPayDetail(out_trade_no.value)
  ordeData.value = JSON.parse(data)
  msg.value = ordeData.value
}
const getTips = () => {
  if (ordeData.value && ordeData.value.type) {
    if (ordeData.value.type == 1) {
      return '如支付后，文章未解锁，请联系客服。'
    } else if (ordeData.value.type == 3) {
      return '如支付后，包时特权未生效，请联系客服。'
    } else {
      return '如支付后，充值未到账，请联系客服。'
    }
  } else {
    return '如支付后，权益未到账，请联系客服'
  }
}

const toPage = () => {
  let toUrl = location.origin
  if (ordeData.value?.type) {
    if (ordeData.value.type == 1) {
      //文章详情
      toUrl += `/pages/detail/index?id=${ordeData.value.id}`
    } else if (ordeData.value.type == 3) {
      //特权订单
      toUrl += `/pages/authorPackageDetail/index?curAuthorId=${ordeData.value.authorId}&packageId=${ordeData.value.packageId}`
    } else {
      //充值订单
      toUrl += `/pages/myInfo/index`
    }
  } else {
    toUrl += `/pages/myInfo/index`
  }
  msg.value = toUrl
  console.log(toUrl)
  var mchData = {
    action: 'jumpOut',
    jumpOutUrl: toUrl, //跳转的页面
  }
  const postData = JSON.stringify(mchData)
  window.parent.postMessage(postData, 'https://payapp.weixin.qq.com')
}
onMounted(() => {
  //初始化小票
  let mchData = {
    action: 'onIframeReady',
    displayStyle: 'SHOW_CUSTOM_PAGE',
    height: 8800,
  }
  let postData = JSON.stringify(mchData)
  window.parent.postMessage(postData, 'https://payapp.weixin.qq.com')
})
onLoad(async (query) => {
  let head = document.querySelector('head')
  let script = document.createElement('script')
  script.type = 'text/javascript'
  script.src = 'https://wx.gtimg.com/pay_h5/goldplan/js/jgoldplan-1.0.0.js'
  head.appendChild(script)
  console.log(query)
  sub_mch_id.value = query.sub_mch_id
  out_trade_no.value = query.out_trade_no
  check_code.value = query.check_code
  getData()
})
</script>
<style lang="scss" scoped>
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.content {
  width: 450rpx;
  height: 400rpx;
  padding: 40rpx;
  background-color: #fff;
  border-radius: 40rpx;
}
</style>
