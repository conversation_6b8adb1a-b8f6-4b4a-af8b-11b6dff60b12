<template>
  <view class="flex flex-col">
    <view class="flex-1 mb-200rpx pt-32rpx px-20rpx pb-50rpx rounded-16rpx bg-white shadow">
<!--      <view class="text-26rpx">-->
<!--        复制链接,到电脑发布-->
<!--        <wd-icon name="file-copy" size="30rpx" class="ml-10rpx" />-->
<!--      </view>-->
      <template v-if="[SCHEME_CATEGORY.JZ, SCHEME_CATEGORY.BD].includes(category)">
        <view class="mt-25rpx mb-10rpx text-32rpx text-black">
          {{ clearanceTxt }}
        </view>
        <template v-for="e in sm" :key="e.matchId">
          <view
            class="flex flex-col pt-20rpx px-18rpx pb-24rpx bg-#fafafa rounded-16rpx mt-20rpx first:mt-0"
          >
            <!-- 竞足 -->
            <template v-if="category === SCHEME_CATEGORY.JZ">
              <view class="flex items-center gap-x-20rpx text-black text-26rpx">
                <text>{{ jzTitle(e) }}</text>
                <text>{{ `${e.shortHomeName} VS ${e.shortAwayName}` }}</text>
              </view>
              <!-- 主玩法 -->
              <view
                v-for="p in e.matchPlays.filter(({ type }) => type === 0)"
                class="flex gap-y-18rpx mt-12rpx mb-22rpx"
              >
                <view
                  v-if="p.playId === PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW"
                  class="flex flex-x-20rpx items-center"
                >
                  <text class="text-26rpx w-120rpx">让球：</text>
                  <text
                    class="center w-132rpx h-36rpx border-1rpx border-solid border-#EA545D text-24rpx text-#EA545D rounded-6rpx"
                  >
                    {{ rqTxt(p.result) }}
                  </text>
                </view>
                <view
                  v-else-if="p.playId === PLAY_TYPE.WIN_LOSE_DRAW"
                  class="flex flex-x-20rpx items-center"
                >
                  <text class="text-26rpx w-120rpx">胜平负：</text>
                  <text
                    class="center w-132rpx h-36rpx border-1rpx border-solid border-#EA545D text-24rpx text-#EA545D rounded-6rpx"
                  >
                    {{ spfTxt(p.result) }}
                  </text>
                </view>
              </view>
              <!-- 附赠玩法 -->
              <view
                class="flex items-start text-26rpx"
                v-if="e.matchPlays.filter(({ type }) => type === 1).length > 0"
              >
                <text class="w-140rpx shrink-0">附赠玩法：</text>
                <view class="flex flex-wrap gap-x-20rpx">
                  <view v-for="p in e.matchPlays.filter(({ type }) => type === 1)" :key="p.playId">
                    <view v-if="p.playId === PLAY_TYPE.SCORE">
                      <text>{{ `比分 ${p.result.split(',').join(' ')}` }}</text>
                    </view>
                    <view v-else-if="p.playId === PLAY_TYPE.BQC">
                      <text>{{ `半全场 ${p.result.split(',').join(' ')}` }}</text>
                    </view>
                    <view v-else-if="p.playId === PLAY_TYPE.JQ">
                      <text>{{ `进球数 ${p.result.split(',').join(' ')}` }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </template>
            <!-- 北单 -->
            <template v-else>
              <view class="flex justify-between text-26rpx">
                <text>{{ format(e.matchTime * 1000, 'MM-DD HH:mm') }}</text>
                <text>{{ e.roundName }}</text>
              </view>
              <view class="mt-12rpx text-26rpx">{{ `${e.homeName} VS ${e.awayName}` }}</view>
              <!-- 主玩法 -->
              <view
                v-for="p in e.matchPlays.filter(({ type }) => type === 0)"
                class="flex gap-y-18rpx mt-12rpx mb-22rpx"
              >
                <view v-if="p.playId === PLAY_TYPE.RQ" class="flex flex-x-20rpx items-center">
                  <text class="text-26rpx w-120rpx">让球：</text>
                  <text
                    class="center w-132rpx h-36rpx border-1rpx border-solid border-#EA545D text-24rpx text-#EA545D rounded-6rpx"
                  >
                    {{ rqTxt(p.result) }}
                  </text>
                </view>
                <view v-else-if="p.playId === PLAY_TYPE.DXQ" class="flex flex-x-20rpx items-center">
                  <text class="text-26rpx w-120rpx">大小球：</text>
                  <text
                    class="center w-132rpx h-36rpx border-1rpx border-solid border-#EA545D text-24rpx text-#EA545D rounded-6rpx"
                  >
                    {{ dxqTxt(p.result) }}
                  </text>
                </view>
                <view
                  v-else-if="p.playId === PLAY_TYPE.WIN_LOSE_DRAW"
                  class="flex flex-x-20rpx items-center"
                >
                  <text class="text-26rpx w-120rpx">胜平负：</text>
                  <text
                    class="center w-132rpx h-36rpx border-1rpx border-solid border-#EA545D text-24rpx text-#EA545D rounded-6rpx"
                  >
                    {{ spfTxt(p.result) }}
                  </text>
                </view>
              </view>
              <!-- 附赠玩法 -->
              <view
                class="flex items-start text-26rpx"
                v-if="e.matchPlays.filter(({ type }) => type === 1).length > 0"
              >
                <text class="w-140rpx shrink-0">附赠玩法：</text>
                <view class="flex flex-wrap gap-x-20rpx">
                  <view v-for="p in e.matchPlays.filter(({ type }) => type === 1)" :key="p.playId">
                    <view v-if="p.playId === PLAY_TYPE.SCORE">
                      <text>{{ `比分 ${p.result.split(',').join(' ')}` }}</text>
                    </view>
                    <view v-else-if="p.playId === PLAY_TYPE.JQ">
                      <text>{{ `进球数 ${p.result.split(',').join(' ')}` }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </template>
          </view>
        </template>
      </template>
      <template
        v-else-if="[SCHEME_CATEGORY.MATCH_LOTTERY, SCHEME_CATEGORY.ANY_NINE].includes(category)"
      >
        <view
          class="mt-20rpx rounded-16rpx text-26rpx text-black border-1rpx border-solid border-#ddd"
        >
          <view class="flex">
            <view class="center w-100rpx h-70rpx">场次</view>
            <view class="center flex-1 h-70rpx">比赛</view>
            <view class="center w-150rpx h-70rpx">专家推荐</view>
          </view>
          <view
            class="flex"
            v-for="({ matchId, homeName, awayName, matchPlays }, index) in scheme.main"
            :key="matchId"
          >
            <view class="center w-100rpx h-70rpx">{{ index }}</view>
            <view class="center flex-1 h-70rpx">{{ `${homeName} VS ${awayName}` }}</view>
            <view class="center w-150rpx h-70rpx">{{ matchPlays[0].result }}</view>
          </view>
        </view>
      </template>

      <!-- 方案标题 -->
      <view
        class="relative my-22rpx pl-18rpx before:absolute before:left-0 before:top-4rpx before:content-['*'] text-28rpx before:text-22rpx before:text-#EA545D"
      >
        方案标题
      </view>
      <wd-input
        :readonly="!canEdit"
        v-model="article.title"
        prop="title"
        placeholder="请输入方案标题，不超过20个汉字字符"
        clearable
        :maxlength="20"
      />
      <!-- 精彩简介 -->
      <view class="mt-18rpx mb-22rpx text-28rpx">精彩简介</view>
      <wd-input
        :readonly="!canEdit"
        v-model="article.intro"
        prop="intro"
        placeholder="请输入方案的精彩简介，不超过50个汉字字符"
        clearable
        :maxlength="50"
      />
      <!-- 免费内容 -->
      <view class="mt-18rpx mb-22rpx text-28rpx">免费内容</view>
      <piaoyi-editor
        class="w-650rpx"
        ref="freeEditorRef"
        v-model="article.freeContents"
        @changes="changeFreeContent"
        @ready="freeEditorReady"
        :photoUrl="photoUrl"
        :maxlength="10000"
      />
      <!-- 付费内容 -->
      <view
        class="relative my-22rpx pl-18rpx before:absolute before:left-0 before:top-4rpx before:content-['*'] text-28rpx before:text-22rpx before:text-#EA545D"
      >
        付费内容
      </view>
      <piaoyi-editor
        class="w-650rpx"
        ref="editorRef"
        v-model="article.contents"
        @changes="changeContent"
        @ready="readyEditor"
        :photoUrl="photoUrl"
        :maxlength="10000"
      />
      <view class="flex items-center">
        <text
          class="w-140rpx relative my-22rpx pl-18rpx before:absolute before:left-0 before:top-4rpx before:content-['*'] text-28rpx before:text-22rpx before:text-#EA545D"
        >
          关联套餐
        </text>
        <wd-radio-group v-model="relatedPackage" shape="dot" inline>
          <wd-radio :value="1">是</wd-radio>
          <wd-radio :value="0">否</wd-radio>
        </wd-radio-group>
      </view>
      <view v-if="relatedPackage">
        <wd-checkbox-group v-model="privilegeIds" cell shape="dot">
          <template v-for="{ name, id } in privilegeList" :key="id">
            <wd-checkbox :modelValue="id">{{ name }}</wd-checkbox>
          </template>
        </wd-checkbox-group>
      </view>
      <view class="flex items-center">
        <text
          class="w-140rpx text-28rpx relative pl-18rpx before:absolute before:left-0 before:top-4rpx before:content-['*'] before:text-22rpx before:text-#EA545D"
        >
          优惠策略
        </text>
        <wd-radio-group shape="button" inline v-model="preferentialValue">
          <template v-for="e in PREFERENTIAL" :key="e.value">
            <wd-radio :value="e.value" custom-class="ck-box">{{ e.label }}</wd-radio>
          </template>
        </wd-radio-group>
      </view>
      <view class="flex justify-between items-center my-30rpx">
        <text
          class="w-140rpx text-28rpx relative pl-18rpx before:absolute before:left-0 before:top-4rpx before:content-['*'] before:text-22rpx before:text-#EA545D"
        >
          设置售价
        </text>
        <view class="flex flex-wrap gap-20rpx flex-1">
          <template v-for="p in ARTICLE_PRICE_TEMPLATE" :key="p">
            <text
              @click="selectPrice(p)"
              v-if="p === '0'"
              class="h-70rpx text-26rpx border-solid border-1rpx border-black border-opacity-10 text-center box-border leading-70rpx rounded-lg"
              style="width: calc((100% - 40rpx) / 3)"
              :style="pStyle(p)"
            >
              免费
            </text>
            <text
              @click="selectPrice(p)"
              v-else
              class="h-70rpx text-26rpx border-solid border-1rpx border-black border-opacity-10 text-center box-border leading-70rpx rounded-lg"
              style="width: calc((100% - 40rpx) / 3)"
              :style="pStyle(p)"
            >
              {{ p }}
            </text>
          </template>
          <text
            @click="toggleCustomizePrice"
            class="h-70rpx text-26rpx border-solid border-1rpx border-black border-opacity-10 text-center box-border leading-70rpx rounded-lg"
            style="width: calc((100% - 40rpx) / 3)"
          >
            自定义
          </text>
        </view>
      </view>
      <template v-if="showCustomizePrice">
        <view class="pl-140rpx">
          <wd-input-number
            v-model="price"
            :precision="2"
            :min="0"
            :max="99999"
            input-width="100%"
            allow-null
            placeholder="请输入自定义价格"
            @change="handleCustomPrice"
          />
        </view>
      </template>
      <!-- 设置初始购买人数 -->
      <view class="flex justify-between items-center h-100rpx border-bottom">
        <text class="shrink-0 text-black text-opacity-90 text-28rpx">设置初始购买人数</text>
        <wd-input-number
          v-model="initBuyCount"
          :min="0"
          :max="99999"
          input-width="100%"
          allow-null
          placeholder="请输入初始购买人数"
          class="goumairenshu pr-30rpx"
        />
      </view>
    </view>
    <!-- 底部 -->
    <view
      class="fixed bottom-0 flex justify-end items-center gap-x-28rpx w-full h-90rpx text-28rpx bg-white"
    >
      <view
        @click="handlePrivious"
        type="info"
        class="center w-160rpx h-60rpx border-1rpx border-solid border-#ddd rounded-8rpx text-#999"
      >
        上一步
      </view>
<!--   todo: 下个迭代版本完善功能   -->
<!--      <view-->
<!--        type="info"-->
<!--        @click="submit(1)"-->
<!--        class="center w-160rpx h-60rpx border-1rpx border-solid border-#ddd rounded-8rpx rounded-8rpx text-#999"-->
<!--      >-->
<!--        保存草稿-->
<!--      </view>-->
      <view
        class="center w-160rpx h-60rpx mr-20rpx bg-#D1302E rounded-8rpx text-white"
        @click="submit(0)"
      >
        发布
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { IMatchScheme } from "@/api/match";
import { getPublishPrivilegeList } from "@/api/member";
import { useMatchSchemeStore } from "@/store/matchScheme";
import { PLAY_TYPE, SCHEME_CATEGORY } from "@/utils/enum";
import { format } from "@/utils/format";
import { isEmpty, uniq } from "lodash-es";
import { ARTICLE_PRICE_TEMPLATE, PREFERENTIAL } from "@/utils/constant";
import { getArticleDetail } from "@/api/article";
import { IArticleDraft, publishArticle } from "@/api/author";

const props = defineProps<{ category: SCHEME_CATEGORY; draft: IArticleDraft | null }>()
const { scheme, setMainScheme, setBonusScheme, changeSchemePlay } = useMatchSchemeStore()

const emit = defineEmits<{
  (e: 'privious')
}>()

const article = ref()

watch(
  article,
  (newValue, oldValue) => {
    // 当 article 的任何属性发生变化时，此回调函数将被触发
    scheme.article = newValue
    // 在这里执行您需要的逻辑
  },
  { deep: true } // 使用 deep: true 来侦听对象内部属性的变化
)

const id = ref<null | number>(null)
const canEdit = ref(1) // 是否可以编辑(0 不能 1 可以)
const relatedPackage = ref(0)
const privilegeList = ref([])
const privilegeIds = ref<number[]>([])
/* 优惠策略 */
const preferentialValue = ref(0)
/* 不中即退 */
const refundType = computed(() => (preferentialValue.value === 2 ? 1 : 0))
/* 设置初始购买人数 */
const initBuyCount = ref(20)

/* 可补单 */
const autoReplacement = computed(() => (preferentialValue.value === 1 ? 1 : 0))

const freeEditorReady = () => {
  if (freeEditorRef.value && article.value?.freeContents) {
    freeEditorRef.value.setOtherContent(article.value.freeContents);
  }
};
const editorReady = ref(false)
const readyEditor = () => {
  if (editorRef.value && article.value?.contents) {
    editorRef.value.setOtherContent(article.value.contents);
  }
};
const photoUrl = import.meta.env.VITE_UPLOAD_BASEURL

function changeFreeContent(v: { html: string }) {
  article.value.freeContents = v.html
}

function changeContent(v: { html: string }) {
  article.value.contents = v.html
}

const showCustomizePrice = ref(false)
const price = ref<string | number>('')
/* 选择模板价格 */
function selectPrice(p: string) {
  if (showCustomizePrice.value || p === price.value) return
  price.value = p
  if (price.value === '0') {
    initBuyCount.value = 0
  } else {
    initBuyCount.value = 20
  }
}

function handleCustomPrice({ value }) {
  initBuyCount.value = value ? 20 : 0
}

/* 切换自定义输入价格或使用模板价格 */
function toggleCustomizePrice() {
  price.value = ''
  showCustomizePrice.value = !showCustomizePrice.value
}

const isFreeReleaseMode = computed(() => isEmpty(scheme.main) && isEmpty(scheme.bonus))

const sm = computed<IMatchScheme[]>(() => {
  const main: IMatchScheme[] = scheme.main;
  const bonus: IMatchScheme[] = scheme.bonus;

// 创建一个 Map 来存储合并后的结果，以 matchId 为键，方便查找和更新
  const mergedMap = new Map<string | number, IMatchScheme>();

// 1. 首先处理 main 数组中的所有项
  main.forEach(mainItem => {
    // 创建 mainItem 的一个副本，特别是 matchPlays 数组，以避免修改原始数据
    mergedMap.set(mainItem.matchId, {
      ...mainItem,
      matchPlays: [...mainItem.matchPlays],
    });
  });

// 2. 接着处理 bonus 数组中的所有项
  bonus.forEach(bonusItem => {
    const existingItem = mergedMap.get(bonusItem.matchId);
    if (existingItem) {
      // 如果 bonusItem 的 matchId 已存在于 map 中（即来自 main 数组）
      // 则将 bonusItem.matchPlays 合并到 existingItem.matchPlays
      existingItem.matchPlays.push(...bonusItem.matchPlays);
    } else {
      // 如果 bonusItem 的 matchId 在 map 中不存在（即此项仅在 bonus 中，或 main 为空）
      // 则将 bonusItem 的一个副本添加到 map 中
      mergedMap.set(bonusItem.matchId, {
        ...bonusItem,
        matchPlays: [...bonusItem.matchPlays],
      });
    }
  });

// 3. 将 Map 中的值转换为数组，得到最终的合并结果
  return Array.from(mergedMap.values())

})

function pStyle(p: string) {
  if (showCustomizePrice.value) return 'background: rgba(0,0,0,0.04);opacity: 0.4'

  if (p === price.value)
    return 'background: rgba(209, 48, 46, 0.10);border: 1px solid #D1302E; color: #D1302E'

  return ''
}

function jzTitle(match: IMatchScheme) {
  const { week, issueNum, matchTime } = match
  return `${week} ${issueNum} ${format(matchTime * 1000, 'MM/DD HH:mm')}`
}

function rqTxt(r: string) {
  return r
    .split(',')
    .slice(1)
    .map((e) => {
      switch (e) {
        case '3':
          return '让胜'
        case '1':
          return '让平'
        case '0':
          return '让负'
        default:
          return ''
      }
    })
    .join('/')
}

function spfTxt(r: string) {
  return r
    .split(',')
    .map((e) => {
      switch (e) {
        case '3':
          return '胜'
        case '1':
          return '平'
        case '0':
          return '负'
        default:
          return ''
      }
    })
    .join('/')
}

function dxqTxt(r: string) {
  return r
    .split(',')
    .slice(0, 2)
    .reduce((p, c, i) => {
      if (i === 0) return `(${c})`
      return `${c === '3' ? '大' : '小'}${p}`
    }, '')
}

// 过关方式
const clearanceTxt = computed(() => {
  const count = uniq([
    ...scheme.main.map((s) => s.matchId),
    ...scheme.bonus.map((s) => s.matchId),
  ]).length
  if (count === 1) return '过关方式：单关'
  return `过关方式：${count}串1`
})

function handlePrivious() {
  if (isFreeReleaseMode.value) return
  emit('privious')
}

function validate() {
  const { title, contents } = article.value
  if (!price.value) {
    uni.showToast({
      title: '请输入文章价格',
      icon: 'none',
    })
    return false
  }

  if (!title) {
    uni.showToast({
      title: '文章标题不能为空',
      icon: 'none',
    })
    return false
  }

  if (price.value && !contents) {
    uni.showToast({
      title: '请填写付费内容',
      icon: 'none',
    })
    return false
  }

  return true
}

function submit(draft: number) {
  try {
    uni.showLoading()
    const valid = validate()
    if (!valid) return

    const matchIds = sm.value.map((s) => s.matchId).join(',')
    const matchScheme = JSON.stringify(sm.value)
    const schemePlay = scheme.schemePlay

    let p: null | number | string = null
    if (price.value) {
      p = price.value.toString()
      p = (p as string).includes('.') ? parseFloat(p as string) : parseInt(p as string)
    }

    let data: any = {
      ...article.value,
      draft,
      refundType: refundType.value,
      autoReplacement: autoReplacement.value,
      price: p,
      privilegeIds: privilegeIds.value.join(','),
      initBuyCount: initBuyCount.value,
      matchIds,
    }

    if (schemePlay && !isEmpty(matchScheme)) {
      data = { ...data, schemePlay, matchScheme }
    }

    data = id.value ? { ...data, id: id.value } : data

    // 提交数据
    publishArticle(data).then((aid) => {
      uni.hideLoading()
      if (draft) {
        uni.showToast({ title: '保存成功', icon: 'success' })
      } else {
        uni.showToast({
          title: '提交成功',
          icon: 'success',
          duration: 3000,
          success: () => {
            uni.redirectTo({ url: `/pages/article/setting/index?id=${aid}` })
          },
        })
        // uni.redirectTo({ url: `/pages/article/setting/index?id=${aid}` })
      }
    })
  } finally {
    uni.hideLoading()
  }
}

const freeEditorRef = ref<null | any>(null)
const editorRef = ref<null | any>(null)


onLoad(async ({ id: _id }) => {
  // 获取套餐包
  try {
    uni.showLoading()
    if (_id || props.draft) {
      const [a, privilege] = await Promise.all([getArticleDetail(_id || props.draft.id), getPublishPrivilegeList()])
      privilegeList.value = privilege
      id.value = _id || props.draft.id

      const {
        matchScheme,
        title,
        intro,
        freeContents,
        contents,
        autoReplacement,
        refundType,
        schemePlay,
        price: p,
        canEdit: c,
        privilegeIds: pids,
      } = a

      canEdit.value = c
      price.value = p

      article.value = {
        title,
        intro,
        freeContents,
        contents,
      }

      privilegeIds.value = pids.split(',')

      if (!autoReplacement && !refundType) {
        preferentialValue.value = 0
      } else if (autoReplacement === 1) {
        preferentialValue.value = 1
      } else if (refundType === 1) {
        preferentialValue.value = 2
      }

      const ms: IMatchScheme[] = JSON.parse(matchScheme)
      let main: IMatchScheme[] = []
      let bonus: IMatchScheme[] = []

      ms.filter((e) => {
        const { matchPlays, ...rest } = e
        if (matchPlays.find((item) => item.type === 0)) {
          main.push({
            ...rest,
            matchPlays: matchPlays.filter((item) => item.type === 0),
          })
        } else {
          bonus.push({
            ...rest,
            matchPlays: matchPlays.filter((item) => item.type === 1),
          })
        }
      })

      changeSchemePlay(schemePlay)
      if (!isEmpty(main)) {
        setMainScheme(main)
      }

      if (!isEmpty(bonus)) {
        setBonusScheme(bonus)
      }
    } else {
      article.value = scheme.article
      freeEditorRef.value.setOtherContent(article.value.freeContents)
      editorRef.value.setOtherContent(article.value.contents)
      const p = await getPublishPrivilegeList()
      privilegeList.value = p
    }
  } finally {
    uni.hideLoading()
  }
})
</script>

<style lang="scss" scoped>
:deep(.wd-input) {
  box-sizing: border-box;
  padding: 5rpx;
  background-color: rgb(250, 250, 250);
  border: 1px solid rgb(241, 241, 241);
  border-radius: 10rpx;

  &.is-not-empty {
    &::after {
      display: none;
    }
  }

  :deep(.wd-input__value) {
    padding: 0 10rpx;
  }
}

:deep(.wd-input-number) {
  width: 100%;

  .wd-input-number__inner {
    width: 100%;
  }

  .wd-input-number__action {
    display: none;
  }

  .wd-input-number__input-border {
    border: none;
  }

  .wd-input-number__input {
    font-size: 14px;
  }
}

.table {
  background-color: red;

  :deep(.wd-table__header) {
    background-color: blue;
  }
}

.ck-box {
  width: 150rpx !important;
  height: 70rpx !important;
  padding: 0 !important;
  border-radius: 8rpx !important;

  :deep(.wd-checkbox__label) {
    --wot-checkbox-button-font-size: 26rpx;
    padding: 0 !important;
    background-color: unset !important;
    border-radius: 8rpx !important;
  }
}
</style>
