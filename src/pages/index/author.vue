<template>
  <view class="position-relative h-full">
    <!-- 统计信息 -->
    <view
      class="flex justify-between pt-40rpx p-x-30rpx pb-40rpx"
      style="border-top: 1rpx solid rgba(121, 121, 121, 0.2)"
    >
      <!-- 头像 名称 -->
      <view class="flex items-center">
        <image
          :src="userStore.userInfo.avatarUrl"
          class="w-120rpx h-120rpx rounded-full mr-20rpx"
        ></image>
        <text style="font-size: 33rpx; color: #333333">{{ userStore.userInfo.nickname }}</text>
      </view>
      <!-- 今日收益 向下 -->
      <view class="flex flex-col items-center justify-center" @click="goReport">
        <text class="text-30rpx text-black text-opacity-50 font-medium leading-42rpx">
          今日销售额
          <image class="w-20rpx h-32rpx" src="@/static/images/author/jt.png"></image>
        </text>
        <text class="text-#D1302E">￥{{ todayIncome }}</text>
      </view>
    </view>

    <!-- 收入统计区域 -->
    <view class="income-statistics">
      <view class="flex justify-between income-content">
        <view class="flex flex-col items-center income-item">
          <text class="income-label">总收入</text>
          <text class="income-value">{{ totalIncome }}元</text>
        </view>
        <view class="flex flex-col items-center income-item" @click="goReportByStep(8)">
          <text class="income-label">今日方案收入</text>
          <text class="income-value">{{ todayArticleIncome }}元</text>
        </view>
        <view class="flex flex-col items-center income-item" @click="goReportByStep(7)">
          <text class="income-label">今日套餐收入</text>
          <text class="income-value">{{ todayPrivilegeIncome }}元</text>
        </view>
      </view>
    </view>

    <!-- 是否判定按钮 -->
    <!-- <view
      class="flex justify-between gap-col-30rpx mt-10rpx p-30rpx bg-white border-b-solid border-b-1rpx border-b-#797979 border-b-opacity-20"
    >
      <text
        :class="{
          'judge-btn': true,
          'is-active': judgeType === JUDGE_TYPE.JUDGED,
        }"
        @click="changeJudgeType(JUDGE_TYPE.JUDGED)"
      >
        已判定
      </text>
      <text
        :class="{
          'judge-btn': true,
          'is-active': judgeType === JUDGE_TYPE.UNJUDGED,
        }"
        @click="changeJudgeType(JUDGE_TYPE.UNJUDGED)"
      >
        未判定
      </text>
    </view> -->
    <!-- 搜索行 -->
    <!-- <view class="flex justify-between p-30rpx bg-white gap-col-10rpx">
      <view class="flex-1">
        <wd-input
          prefixIcon="search"
          type="text"
          custom-class="form-item"
          v-model="formData.title"
          @input="handleInput"
        />
      </view>
      <view class="flex-1">
        <wd-calendar use-default-slot type="date" v-model="formData.dateTime" @confirm="filteData">
          <wd-input
            prefixIcon="calendar"
            custom-class="form-item"
            placeholder="年/月/日"
            v-model="displayDate"
            readonly
          />
        </wd-calendar>
      </view>
    </view> -->
    <wd-tabs v-model="tab" class="mt-[-10rpx] custom-tabs">
      <wd-tab title="在售方案">
        <!-- 文章组件 -->
        <author-article ref="authorArticleRef" :judgeType="judgeType" @flash="refreshData" />
      </wd-tab>
      <wd-tab title="历史方案">
        <!-- 历史文章组件 -->
        <author-history-article ref="authorHistoryArticleRef" @flash="refreshData" />
      </wd-tab>
      <wd-tab title="我的套餐">
        <!-- 套餐组件 -->
        <author-package ref="authorPackageRef" @flash="refreshData" />
      </wd-tab>
    </wd-tabs>

    <wd-fab
      v-if="userStore.userInfo?.author === 1"
      :draggable="true"
      :expandable="false"
      class="mt-[-80rpx]"
      position="right-bottom"
    >
      <template #trigger>
        <view class="revice" @click="openPubliction">
          <image
            v-if="tab === 2"
            class="w-90rpx h-90rpx"
            src="https://sacdn.850g.com/football/static/add-package.png"
          />
          <image
            v-else
            class="w-90rpx h-90rpx"
            src="https://sacdn.850g.com/football/static/add-scheme.png"
          />
        </view>
      </template>
    </wd-fab>
  </view>
  <publication-type ref="publicationRef" />
</template>

<script lang="ts" setup>
import { IAuthorArticleParams, getAuthorArticles } from '@/api/article'
import authorArticle from './components/authorArticle/index.vue'
import authorPackage from './components/authorPackage/index.vue'
import authorHistoryArticle from './components/authorHistoryArticle/index.vue'
import { formatDate } from '@/utils/format'
import { IAuthorStatistic, getAuthorStatistic, getEditorType } from '@/api/author'
import { useUserStore } from '@/store'
import { JUDGE_TYPE } from '@/utils/enum'
import publicationType from '../article/relaese/components/publicationType.vue'
import { useMatchSchemeStore } from '@/store/matchScheme'

const userStore = useUserStore()
const authorArticleRef = ref(null)
const authorHistoryArticleRef = ref(null)
const authorPackageRef = ref(null)

const tab = ref<number>(0)
const judgeType = ref(JUDGE_TYPE.UNJUDGED)

watch(tab, (v) => {
  console.log(v)
})

const publicationRef = ref()

function openPubliction() {
  // publicationRef.value.open()
  // uni.navigateTo({
  //   url: '/pages/article/relaese/index',
  // })
  toRelaese()
}
const { clearScheme } = useMatchSchemeStore()
async function toRelaese() {
  if (tab.value === 0) {
    // const editorType = await getEditorType();
    // if (editorType) {
    //   uni.navigateTo({
    //     url: "/pages/article/relaese/index"
    //   });
    // } else {
    //   uni.navigateTo({
    //     url: "/pages/article/relaese/index_old"
    //   });
    // }
    clearScheme()
    uni.navigateTo({ url: '/pages/release/index' })
  } else if (tab.value === 2) {
    toPackage()
  }
}

// 跳转到套餐设置页面
function toPackage() {
  uni.navigateTo({
    url: '/pages/index/adthorAddPackage',
  })
}

const goReport = () => {
  uni.navigateTo({
    url: '/pages/report/index',
  })
}

const goReportByStep = (step: number) => {
  uni.navigateTo({
    url: `/pages/report/index?step=${step}`,
  })
}

const statistic = ref<IAuthorStatistic | null>(null)

// 今日收益
const todayIncome = computed(() => (statistic.value ? statistic.value.todayIncome : '--'))

// 总收入
const totalIncome = computed(() => (statistic.value ? statistic.value.totalIncome : '--'))
// 今日购买人数
const todayConsumer = computed(() => (statistic.value ? statistic.value.todayConsumer : '--'))

// 今日方案收入
const todayArticleIncome = computed(() =>
  statistic.value ? statistic.value.todayArticleIncome : '--',
)

// 今日套餐收入
const todayPrivilegeIncome = computed(() =>
  statistic.value ? statistic.value.todayPrivilegeIncome : '--',
)

// 刷新数据
function refreshData() {
  if (tab.value === 0 && authorArticleRef.value) {
    authorArticleRef.value.refresh()
  } else if (tab.value === 1 && authorHistoryArticleRef.value) {
    authorHistoryArticleRef.value.refresh()
  } else if (tab.value === 2 && authorPackageRef.value) {
    authorPackageRef.value.refresh()
  }
}

function changeJudgeType(type: JUDGE_TYPE) {
  if (judgeType.value !== type) {
    judgeType.value = type
    refreshData()
  }
}

onMounted(() => {
  getAuthorStatistic().then((s) => {
    statistic.value = s
  })
})
</script>

<script lang="ts">
export default {
  options: {
    styleIsolation: 'shared', // 解除样式隔离
  },
}
</script>

<style lang="scss" scoped>
.judge-btn {
  flex: 1;
  height: 80rpx !important;
  font-size: 30rpx;
  line-height: 80rpx;
  color: rgba(0, 0, 0, 0.5) !important;
  text-align: center;
  background: rgba(0, 0, 0, 0.02) !important;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12rpx !important;

  &.is-active {
    color: #d1302e !important;
    background: rgba(209, 48, 46, 0.1) !important;
    border: 1px solid #d1302e;
  }
}

:deep() {
  .form-item {
    height: 64rpx;
    padding-left: 10rpx;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12rpx;

    &.wd-input.is-not-empty {
      &::after {
        display: none;
      }
    }
  }

  .custom-tabs {
    height: calc(100% - 340rpx);

    .wd-tabs__nav {
      padding-top: 10rpx;
      overflow: hidden;
      background-color: #f5f5f5;
      border-radius: 20rpx 20rpx 0 0;
      box-shadow: 0px -5px 10px -5px rgba(0, 0, 0, 0.3);
    }

    .wd-tabs__container {
      background-color: #f5f5f5;
    }

    .wd-tabs__line {
      bottom: 0;
    }
  }
}

.revice {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90rpx;
  height: 90rpx;
  text-align: center;
  background-color: #fff;
  border-radius: 50%;
}

.income-statistics {
  position: relative;
  width: 692rpx;
  padding: 0;
  margin-left: 25rpx;
  overflow: hidden;
  background-image: url('@/static/images/author/srmx.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .income-content {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    padding: 30rpx 20rpx;
  }

  .income-item {
    position: relative;
    flex: 1;
    padding: 0 20rpx;
    text-align: center;

    &:not(:last-child)::after {
      position: absolute;
      top: 50%;
      right: 0;
      width: 1px;
      height: 60%;
      content: '';
      background-color: rgba(255, 255, 255, 0.3);
      transform: translateY(-50%);
    }
  }

  .income-label {
    margin-bottom: 10rpx;
    font-size: 28rpx;
    color: #ddd3d1;
    opacity: 0.8;
  }

  .income-value {
    font-size: 30rpx;
    // font-weight: bold;
    color: #ffffff;
  }
}

:deep() {
  .wd-tabs__nav {
    height: 75rpx;
  }

  .wd-tabs__container {
    height: calc(100% - 85rpx);
  }
}
</style>
