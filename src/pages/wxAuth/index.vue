<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '神鱼体育-微信授权',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="px-[60rpx] text-center">
    <!-- <view class="text-center">
            <image src="/static/logo.svg" style="width: 200rpx;height: 200rpx;" />
        </view> -->
    <view class="text-area text-center my-[80rpx] text-[#666] font-size-[30rpx] leading-[50rpx]">
      <view>为了更好的体验和保存您的数据</view>
      <view>微信一键登录，更快捷安全</view>
    </view>
    <view class="text-center">
      <wd-button class="login" @click="login">微信授权</wd-button>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store'
import { getWXH5LoginCode } from '@/utils/wxh5Login'
import { generateSign } from '@/utils/authCheck'

const userStore = useUserStore()
const type = ref('')
const articleId = ref()
const authorId = ref()
const toPagePath = ref('')
const shareType = ref(0)

const nobackAuthor = ref([68033, 73669, 68377, 77939])
const login = async () => {
  // 获取公众APPID
  const localUrl = window.location.origin + toPagePath.value
  console.log(localUrl)
  getWXH5LoginCode(localUrl)
}

const toPage = () => {
  console.log(toPagePath.value)
  if (!toPagePath.value) {
    uni.switchTab({ url: '/pages/index/index' })
  } else {
    uni.navigateTo({
      url: toPagePath.value,
    })
  }
}

const isInit = ref(false)

onShow(() => {
  if (isInit.value) {
    if (userStore.isLogined) {
      toPagePath.value = null
      toPage()
    } else {
      login()
    }
  }
})

onLoad(async (query) => {
  console.log(query)
  type.value = query.type
  articleId.value = query.articleId
  authorId.value = query.authorId
  shareType.value = query.shareType || 0
  const ts = query.ts || 0
  const op = query.op
  let sign = query.sign
  if (type.value === 'articleDetail') {
    sign = generateSign('/pages/detail/index', ts, op)
    toPagePath.value = `/pages/detail/index?auto=1&scan=1&id=${articleId.value}&shareType=${shareType.value}&articleId=${articleId.value}&ts=${ts}&op=${op}&sign=${sign}`
  } else if (type.value === 'authorHome') {
    if (query.authorId && nobackAuthor.value.includes(Number(query.authorId))) {
      toPagePath.value = `/pages/author/info/index?auto=1&scan=1&authorId=${authorId.value}`
    } else {
      toPagePath.value = `/pages/author/info/index?auto=1&scan=0&authorId=${authorId.value}`
    }
  } else if (type.value === 'tvHome') {
    toPagePath.value = `/pages/index/index`
  }
  if (userStore.isLogined) {
    toPage()
  } else {
    login()
  }
  setTimeout(() => {
    isInit.value = true
  }, 200)
})
</script>
<style lang="scss" scoped>
.login {
  width: 500rpx;
  background: #2aae67 !important;
}
</style>
