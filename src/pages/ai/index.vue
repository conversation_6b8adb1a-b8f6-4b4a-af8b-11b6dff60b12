<route lang="json5">
{
  style: {
    navigationBarTitleText: '对话',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="center">
  </view>
</template>

<script lang="ts" setup>
import { ref, nextTick } from 'vue';
import { storeToRefs } from "pinia";
import { useUserStore } from "@/store";
import { aiChatList } from "@/api/aiChat";

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const deepChatRef = ref(null);


const chatList = ref([])

onMounted(() => {

  aiChatList().then(res => {
    console.log(`🚀 ~ aiChatList ~ res:`, res)
    // res响应格式
    /* ref = {
        "code": 0,
        "data": [
            {
                "authorId": 6,
                "nickname": "张勇",
                "avatar": "https://sacdn.850g.com/test/pictrue//WfUyhAvHxem7g0tJiLm4z8mHwNscaKUd_1733823247085.png",
                "articleNum": null,
                "qq": "229594652",
                "wxQrcodeUrl": null,
                "wxId": null,
                "wxWorkId": 1
            },
            {
                "authorId": 11,
                "nickname": "花花",
                "avatar": "https://thirdwx.qlogo.cn/mmopen/vi_32/mT9QXkoQT8W4RSkavHNWtm82FcDv13QoGI7Q13PrvzwTFXcibFej5dfLTiaSWibwk5vftH89pCbFiafbmIVhlHeOGrDr5iccEHTG79xsmqTic4JyA/132",
                "articleNum": null,
                "qq": "2298254620",
                "wxQrcodeUrl": null,
                "wxId": null,
                "wxWorkId": 4
            }
        ],
        "msg": ""
    } */
  })
});


</script>
<style lang="scss" scoped></style>
