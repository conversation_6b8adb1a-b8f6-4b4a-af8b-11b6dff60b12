<route lang="json5">
{
  style: {
    navigationBarTitleText: 'AI',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="center">

    <!-- :images='{"button": {"position": "outside-left"}}' -->


    <deep-chat ref="deepChatRef" style="border-radius: 10px; height: calc(100vh - 120px); width: 96vw;" :connect="connect" :history="history" :introMessage='introMessage'
      :messageStyles='{
        default: {
          shared: { bubble: { fontSize: "0.8rem" } },
          ai: { bubble: { backgroundColor: "#F2F2F2", color: "#333" } }
        },
        intro: { bubble: { backgroundColor: "white", color: "#2c3e50", padding: 0 } },
        html: { suggestion: { bubble: { backgroundColor: "unset", padding: "0px" } } },
      }' :textInput='{ placeholder: { text: "向我提问...", style: { color: "#bcbcbc" } } }' :requestInterceptor="requestInterceptor" :htmlClassUtilities="htmlClassUtilities"
      :responseInterceptor="responseInterceptor"></deep-chat>
  </view>
</template>

<script lang="ts" setup>
import "@google-web-components/google-chart/google-chart.js";
import { ref, nextTick } from 'vue';
import 'deep-chat';
import { storeToRefs } from "pinia";
import { useUserStore } from "@/store";
import { aiChat } from "@/api/aiChat";

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const deepChatRef = ref(null);
const authorId = ref(11); // 用于存储作者ID

onMounted(() => {

  // 获取Ai聊天欢迎信息
  aiChat({ msg: 'welcomeMsg' })
    .then((res: any) => {
      const data = JSON.parse(res.content);
      const botInfo = res.botInfo || {};

      uni.setNavigationBarTitle({ title: botInfo.authorName });

      introMessage.value = {
        html: `
          <div>
            <div style="text-align: center; position: relative; bottom: -20px;">
              <img src='${botInfo.authorAvatar}' alt="神鱼AI助手" style="width: 3.5rem; height: 3.5rem; border-radius: 50%; margin-bottom: 10px;" />
            </div>
            <div style="background-color: #F2F2F2; padding: 20px 10px; border-radius: 10px;">
              <div style="font-weight: bold; font-size: .9rem;">${data.title}</div>
              <div style="white-space: pre-wrap;">${data.description}</div>
            </div>
          </div>
        `
      };

      if (res.questions && res.questions.length > 0) generateTemplateMessage(res.questions)
    });
});

onShow(() => {
  nextTick(() => {
    if (deepChatRef.value) {
      deepChatRef.value.scrollToBottom();
    }
  });
})

const connect = {
  url: 'http://*************:48080/app-api/command/chat',
  method: 'POST'
}

const introMessage = ref({});
const templateType = ref(null)

// 发送请求前的拦截器
const requestInterceptor = (requestDetails: any) => {
  console.log(`🚀 ~ 请求:`, requestDetails)
  requestDetails.headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${userInfo.value.token}`,
  };
  // 优先使用 templateType，如果没有则使用用户输入的消息
  requestDetails.body = { msg: requestDetails.body.messages[0].text };
  if (templateType.value) {
    requestDetails.body.templateKey = templateType.value; // 添加模板类型
  }
  if (authorId.value) {
    requestDetails.body.authorId = authorId.value; // 添加作者ID
  }
  templateType.value = null; // 重置 templateType
  return requestDetails;
};

// 响应处理拦截器
const responseInterceptor = (response: any) => {
  console.log(`🚀 ~ 响应:`, response)
  let msg;
  if (response.data.templateType === 'recommendPlan') {
    const content = JSON.parse(response.data.content)
    console.log(`🚀 ~ responseInterceptor ~ content:`, content)
    const articleList = content.articleInfoList || [];

    msg = {
      html: `
    <div>
      <div>${content.msg}</div>
      <div style="font-weight: bold;">
        ${articleList.map((article, index) => `
          <div style="cursor: pointer; padding: 5px 0;">
            <span class="u-article-item" data-articleId="${article.id}" data-articleTitle="${article.title}" style="text-decoration: underline">${index + 1}. ${article.title}</span>
            <span style="color: #DA0A23;">(${article.priceStr})</span>
          </div>
        `).join('')}
      </div>
    </div>
  `, role: 'ai'
    }
  } else if (response.data.contentType === 'chart') {
    // 处理图表类型的响应
    let content = JSON.parse(response.data.content);
    console.log(`🚀 ~ responseInterceptor ~ content:`, content)
    // content格式示例 模拟近30天的数据
    /* content = {
        chartData: [
            { xAxis: "06-16", yAxis: "1" },
            { xAxis: "06-17", yAxis: "2" },
            { xAxis: "06-18", yAxis: "3" },
            { xAxis: "06-19", yAxis: "4" },
            { xAxis: "06-20", yAxis: "5" },
            { xAxis: "06-21", yAxis: "4" },
            { xAxis: "06-22", yAxis: "3" },
            { xAxis: "06-23", yAxis: "2" },
            { xAxis: "06-24", yAxis: "1" },
            { xAxis: "06-25", yAxis: "2" },
            { xAxis: "06-26", yAxis: "3" },
            { xAxis: "06-27", yAxis: "4" },
            { xAxis: "06-28", yAxis: "5" },
            { xAxis: "06-29", yAxis: "6" },
            { xAxis: "06-30", yAxis: "7" },
            { xAxis: "07-01", yAxis: "8" },
            { xAxis: "07-02", yAxis: "9" },
            { xAxis: "07-03", yAxis: "10" },
            { xAxis: "07-04", yAxis: "11" },
            { xAxis: "07-05", yAxis: "12" },
            { xAxis: "07-06", yAxis: "13" },
            { xAxis: "07-07", yAxis: "14" },
            { xAxis: "07-08", yAxis: "15" },
            { xAxis: "07-09", yAxis: "16" },
            { xAxis: "07-10", yAxis: "17" },
            { xAxis: "07-11", yAxis: "18" },
            { xAxis: "07-12", yAxis: "19" },
            { xAxis: "07-13", yAxis: "20" },
            { xAxis: "07-14", yAxis: "21" },
            { xAxis: "07-15", yAxis: "22" },
            { xAxis: "07-16", yAxis: "23" },
        ],
        type: "line", // column, bar, area , line
        xUnit: "日期",
        yUnit: "胜率"
    }; */
    msg = {
      html: `
        <div style="width: 100%">
          <google-chart
            type="${content.type}"
            style="width: 80vw; height: 200px"
            data='[["${content.xUnit}", "${content.yUnit}"], ${content.chartData.map(item => `["${item.xAxis}", ${item.yAxis}]`).join(', ')}]'
            options='{"legend": "none", "chartArea": {"left": 30, "top": 20, "width": "87%", "height": "75%"}}'>
          </google-chart>
        </div>
      `,
      role: response.data.role,
    };
  } else {
    msg = { text: response.data.content, role: response.data.role };
  }

  const questions = response.data.questions
  if (questions && questions.length > 0) generateTemplateMessage(questions)

  return msg;
};

const history = ref([
  /* { role: 'user', text: 'hello' },
  {
    html: `
      <div>
        <div style="margin-bottom: 10px">折线图表示例:</div>
        <google-chart
          type="line"
          style="width: 220px; height: 200px"
          data='[["地区", "温度"], ["长沙", 26], ["北京", 18], ["上海", 24], ["广州", 30]]'
          options='{"legend": "none"}'>
        </google-chart>
      </div>
      `,
    role: 'ai',
  } */
]);

// 处理DeepChat组件的HTML类名 可用于添加自定义事件或样式
const htmlClassUtilities = {
  ['u-article-item']: {
    events: {
      click: (event: any) => {
        const articleId = event.target.getAttribute('data-articleId');
        const articleTitle = event.target.getAttribute('data-articleTitle');
        if (articleId && articleTitle) {
          uni.navigateTo({ url: `/pages/detail/index?id=${articleId}` });
        }
      }
    },
  },
  ['u-suggestion-query']: {
    events: {
      click: (event: any) => {
        const suggestionText = event.target.textContent;
        templateType.value = event.target.getAttribute('data-template') || null
        console.log(`🚀 ~ 发送推荐模版内容:`, { suggestionText, templateType: templateType.value });
        deepChatRef.value.submitUserMessage({ text: suggestionText, role: 'user' });
      }
    },
  }
}

// 生成推荐问题的模板消息
const generateTemplateMessage = (questions) => {
  const templateMessages = {
    html: `
          <div>
            我猜你或许想问:
            ${questions.map((q) => `
              <button class="deep-chat-button u-suggestion-query" style="margin-top: 5px; display: block; color: #3D89B2; font-size: 0.8rem" data-template="${q.templateKey}">${q.question}</button>
            `).join('')}
          </div>`,
    role: 'suggestion',
  }
  console.log(`🚀 ~ generateTemplateMessage ~ templateMessages:`, templateMessages)

  // 需要等待 DeepChat 组件完全渲染后再添加消息
  setTimeout(() => {
    console.log(`🚀 ~ deepChatRef:`, deepChatRef)
    if (deepChatRef.value) {
      deepChatRef.value.addMessage(templateMessages);
    }
  }, 500);
};



/* 
const VULCAN_ENGINE_API_KEY = 'b4a6a025-61c9-4056-a723-9c38bbb2b6e3'; 
const connect = {
  url: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
  method: 'POST'
  headers: {
    'Content-Type': 'application/json',
    // 请替换为您的实际火山引擎API Key
    'Authorization': `Bearer ${VULCAN_ENGINE_API_KEY}`, 
  }
} 
const requestInterceptor = (requestDetails: any) => {
  console.log(`🚀 ~ 请求:`, requestDetails)
  requestDetails.body = {
    model: 'ep-20250619171709-wnrc5',
    messages: [
      {
        content: [
          {
            text: requestDetails.body.messages[0].text,
            type: 'text'
          }
        ],
        role: 'user',
      }
    ]
  };
  return requestDetails;
};

const responseInterceptor = (response: any) => {
  console.log(`🚀 ~ 响应:`, response)

  // return { text: response.choices[0].message.content, role: response.choices[0].message.role };
  return { text: '**未查询到相关问题答案，默认为您推荐方案：**  \r\n- [支付上限测试002(<font color="red">5000鱼币</font>)](http://localhost:48080/pages/detail/index?id=848  )\r\n- [新增支付测试001(<font color="red">0.01鱼币</font>)](http://localhost:48080/pages/detail/index?id=843  )\r\n- [12(<font color="red">88鱼币</font>)](http://localhost:48080/pages/detail/index?id=204  )\r\n', role: response.choices[0].message.role };
};  
*/

</script>
<style lang="scss" scoped></style>
